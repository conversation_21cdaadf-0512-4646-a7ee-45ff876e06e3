import {
  ActionsEnum,
  FeaturesEnum,
  UserSubFeaturesEnum,
} from "../../services/permission.service";
import { Component, HostListener, OnInit, ViewChild } from "@angular/core";
import { MatMenuTrigger } from "@angular/material/menu";
import { CurrencyService } from "src/app/services/currency.service";

// Interface for quarterly data with different methodologies
export interface QuarterData {
  dailyAverage?: number;
  asOnStartDate?: number;
  asOnLatestDate?: number;
}

// Interface for period data
export interface PeriodData {
  [key: string]: QuarterData;
}

// Interface for data structure
export interface FxRateData {
  fromCurrencyCode: string;
  toCurrencyCode: string;
  periods: PeriodData;
}

@Component({
  selector: "app-fxrates",
  templateUrl: "./fxrates.component.html",
  styleUrls: ["./fxrates.component.scss"],
})
export class FxratesComponent implements OnInit {
  selectedFxPreference: any = null;
  feature: typeof FeaturesEnum = FeaturesEnum;
  subFeature: typeof UserSubFeaturesEnum = UserSubFeaturesEnum;
  actions: typeof ActionsEnum = ActionsEnum;
  
  // Data arrays
  fxrateslist: FxRateData[] = [];
  fxrateslistResults: any[] = [];
  fxrateslistResultsClone: any[] = [];
  systemApiBulkUploadDataSet: any[] = [];
  fxratePeriods: string[] = [];
  // UI properties
  isEnableView: boolean = false;
  // Selected methodologies (multiple can be selected)
  selectedMethodologies: string[] = ['Daily Average'];
  
  // Map display names to data property names
  methodologyToProperty = {
    'Daily average': 'dailyAverage',
    'As on latest date': 'asOnLatestDate',
    'As on day before start date': 'asOnStartDate',
    'Average': 'average'
  };
  /**
   * Create a safe field id for kendo column definitions to avoid issues with
   * period strings that contain dashes or other non-identifier characters.
   */
  sanitizeFieldId(period: string, methodology: string): string {
    const property = this.methodologyToProperty[methodology] || 'value';
    const safePeriod = String(period).replace(/[^a-zA-Z0-9_]/g, '_');
    return `col_${safePeriod}_${property}`;
  }

  /**
   * Format a Date-like value to YYYY-MM-DD (no timezone/time component)
   */
  private formatDateOnly(value: any): string | null {
    if (!value) return null;
    const d = value instanceof Date ? value : new Date(value);
    if (isNaN(d.getTime())) return null;
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Update results arrays
   */
  private updateResults(): void {
    // Build display rows with flattened dynamic fields for the grid
    const displayRows = this.fxrateslist.map(row => {
      const display: any = { ...row };
      this.fxratePeriods.forEach(period => {
        this.selectedMethodologies.forEach(methodology => {
          const propertyName = this.methodologyToProperty[methodology] || this.methodologyToProperty['Daily average'];
          const fieldName = this.sanitizeFieldId(period, methodology);
          const value = row?.periods?.[period]?.[propertyName];
          display[fieldName] = value ?? null;
        });
      });
      return display;
    });
    this.fxrateslistResults = displayRows;
    this.fxrateslistResultsClone = [...displayRows];
  }

  /**
   * Get value for a specific period and methodology
   * This makes the methodology linking explicit in the code
   */
  getValueForMethodology(rowData: FxRateData, periodKey: string, methodology: string): number | undefined {
    // Get the property name for this methodology
    const propertyName = this.methodologyToProperty[methodology];
    
    // Return the value for this period and methodology
    return rowData?.periods?.[periodKey]?.[propertyName];
  }
  // Reference to matMenuTrigger to open/close the popup menu
  @ViewChild('masterMenuTrigger', { static: false }) masterMenuTrigger: MatMenuTrigger;
  fxrateslistColumns = [];
  frozenCols: any = [
    { field: "fromCurrencyCode", header: "From Currency" },
    { field: "toCurrencyCode", header: "To Currency" },
  ];
  fxSource = [
    { source: 'Bulk Upload' },
    { source: 'System API' }
  ];
  selectedFxSource: string = 'System API';
  fxOptions: any;
  isDefault: boolean = true;
  isLoader:boolean = false;
  searchText: string = '';
  constructor(private currencyservice: CurrencyService) {}

  ngOnInit(): void {
    this.getFxRateFilterOptions(false);
    this.selectedFxSource = 'System API';
    this.isEnableView = true;
  }

  /**
   * Filter grid data based on search text
   */
  onFilter(value: string): void {
    if (!value) {
      this.fxrateslistResults = [...this.fxrateslistResultsClone];
      return;
    }
    
    const filterValue = value.toLowerCase();
    this.fxrateslistResults = this.fxrateslistResultsClone.filter(item => {
      // Search in from/to currency codes
      if (item.fromCurrencyCode?.toLowerCase().includes(filterValue) ||
          item.toCurrencyCode?.toLowerCase().includes(filterValue)) {
        return true;
      }
      
      // Search in period data values
      if (item.periods) {
        return Object.keys(item.periods).some(periodKey => {
          const periodData = item.periods[periodKey];
          return Object.values(periodData).some(val => 
            val?.toString().toLowerCase().includes(filterValue)
          );
        });
      }
      
      return false;
    });
  }

  // Get the original API method (keeping for reference)
  getBulkuploadfxrates() {
    this.isLoader = true;
    let payload = {
      "fxRateSource": this.selectedFxSource ?? "System API",
      "fromDate": this.formatDateOnly(this.selectedFxPreference?.fromDate),
      "toDate": this.formatDateOnly(this.selectedFxPreference?.toDate),
      "currencyPair": this.selectedFxPreference?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ?? this.fxOptions?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ,
      "periodSource": this.selectedFxPreference?.period ?? "1 YR (Last 1 year)",
      "fyEnd": (this.selectedFxPreference?.financialYearEnd || [])
                  .map((item: any) => item?.fyEndName || item)
                  .join(",") || "December",
      "frequencies": this.selectedFxPreference?.frequency?.map((item: any) => item?.frequencyName || item)?.join(",") ?? "Quarterly",
      "methodologies": (this.selectedFxPreference?.fxMethodology || [])
                          .map((item: any) => item?.methodologyId || item)
                          .join(",") || "1"

    }
  
    this.currencyservice.GetFxratesByPreference(payload).subscribe((resp: any) => {
      this.isLoader = false;
      this.masterMenuTrigger.closeMenu();
      this.fxrateslist = [];
      const periods: string[] = Array.isArray(resp?.periods) ? resp.periods : [];
      const methodologies: string[] = Array.isArray(resp?.methodologies) ? resp.methodologies : [];
      this.fxratePeriods = periods;

      const mapMethodologyToProperty = (name: string): string | undefined => {
        if (!name) return undefined;
        const trimmed = String(name).trim();
        return this.methodologyToProperty[trimmed] || this.methodologyToProperty[trimmed.replace(/\s+/g, ' ')];
      };

      // Respect API methodologies if present (only keep recognized ones)
      if (methodologies.length) {
        const valid = methodologies.filter(m => !!mapMethodologyToProperty(m));
        if (valid.length) {
          this.selectedMethodologies = valid;
        }
      }

      // Build grid rows from groupedResult
      const grouped: any[] = Array.isArray(resp?.groupedResult) ? resp.groupedResult : [];
      this.fxrateslist = grouped.map(group => {
        const periodsData: PeriodData = {};
        periods.forEach(p => { periodsData[p] = {}; });
        const rateValues: any[] = Array.isArray(group?.rateValues) ? group.rateValues : [];
        rateValues.forEach(rv => {
          const periodKey: string = rv?.periodName;
          const propertyName = mapMethodologyToProperty(rv?.methodologyName);
          if (periodKey && propertyName) {
            if (!periodsData[periodKey]) periodsData[periodKey] = {};
            periodsData[periodKey][propertyName] = rv?.rate ?? undefined;
          }
        });
        const row: FxRateData = {
          fromCurrencyCode: group?.fromCurrencyCode,
          toCurrencyCode: group?.toCurrencyCode,
          periods: periodsData
        };
        return row;
      });

      this.updateResults();

      this.isEnableView = true;
    });
  }

  getFxRateFilterOptions(isBulkUpload: boolean) {
    this.isLoader = true;
    this.currencyservice.getFxRateFilterOptions(isBulkUpload).subscribe({
      next: (options) => {
        this.isLoader = false;
        this.fxOptions = options;
        // If API provides fxSource, use it, else keep default
        if (options.fxSource && Array.isArray(options.fxSource)) {
          this.fxSource = options.fxSource;
        }
        if(this.isDefault)
        {
          this.getBulkuploadfxrates();
        }
      },
      error: (error) => {
        // Handle error here
        this.isLoader = false;
        console.error(error);
      },
    });
  }

  onFxSourceChange(value: string) {
    this.isDefault = false;
    this.selectedFxSource = value;
    this.fxrateslist = [];
    this.fxrateslistResults = [];
    this.fxrateslistResultsClone = [];
    this.fxratePeriods = [];
    this.selectedMethodologies = [];
    this.fxOptions = null;
    this.isDefault = true;
    if (value === 'Bulk Upload') {
      this.getFxRateFilterOptions(true);
    } else if (value === 'System API') {
      this.getFxRateFilterOptions(false);
    }
  }

  // Handler for form submit from child
  onFxRatesFormSubmit(formData: any) {
    this.selectedFxPreference = formData;
    // Close the configuration menu immediately after Apply
    this.masterMenuTrigger?.closeMenu();
    this.getBulkuploadfxrates();
  }

  downloadFxRates() {
    this.isLoader = true;
    // Use the same payload as getBulkuploadfxrates
    let payload = {
      "fxRateSource": this.selectedFxSource ?? "System API",
      "fromDate": this.formatDateOnly(this.selectedFxPreference?.fromDate),
      "toDate": this.formatDateOnly(this.selectedFxPreference?.toDate),
      "currencyPair": this.selectedFxPreference?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ?? this.fxOptions?.fxRateCurrencyModels
        ?.flatMap((item: any) =>
          item.children && Array.isArray(item.children)
            ? item.children.map((child: any) => child.value)
            : []
        )?.join(",") ,
      "periodSource": this.selectedFxPreference?.period ?? "1 YR (Last 1 year)",
      "fyEnd": (this.selectedFxPreference?.financialYearEnd || [])
                  .map((item: any) => item?.fyEndName || item)
                  .join(",") || "December",
      "frequencies": this.selectedFxPreference?.frequency?.map((item: any) => item?.frequencyName || item)?.join(",") ?? "Quarterly",
      "methodologies": (this.selectedFxPreference?.fxMethodology || [])
                          .map((item: any) => item?.methodologyId || item)
                          .join(",") || "1"
    };

    this.currencyservice.downloadFxRates(payload).subscribe({
      next: (response: any) => {
        this.isLoader = false;
        // Create a blob from the response
        const blob = new Blob([response], { type: 'application/octet-stream' });
        
        // Create a link element to trigger the download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // Set the filename - use current date if no specific name is available
        const currentDate = new Date().toISOString().split('T')[0];
        link.download = `FX_Rates_${currentDate}.xlsx`;
        
        // Append to the document, click it, and remove it
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      error: (error) => {
        this.isLoader = false;
        console.error('Error downloading FX rates:', error);
      }
    });
  }
}
