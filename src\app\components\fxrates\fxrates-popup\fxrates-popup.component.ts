
import { Component, OnInit, Input, OnChanges, SimpleChanges, ChangeDetectorRef } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CurrencyService } from "src/app/services/currency.service";
import { Output, EventEmitter } from '@angular/core';

// Constants for magic strings and repeated values
const PERIOD_RANGE = 'Period Range';
const PERIOD_FORM_CONTROL = 'period';
const FROM_DATE_FORM_CONTROL = 'fromDate';
const TO_DATE_FORM_CONTROL = 'toDate';
const FREQUENCY_FORM_CONTROL = 'frequency';
const FINANCIAL_YEAR_END_FORM_CONTROL = 'financialYearEnd';
const CURRENCY_SELECTION_FORM_CONTROL = 'currencySelection';
const FX_METHODOLOGY_FORM_CONTROL = 'fxMethodology';
const MONTHLY = 'Monthly';
const DAILY = 'Daily';
const WEEKLY = 'Weekly';
const FREQUENCY_NAMES = [MONTHLY, DAILY, WEEKLY];
const BULK_UPLOAD = 'Bulk Upload';

@Component({
  selector: "app-fxrates-popup",
  templateUrl: "./fxrates-popup.component.html",
  styleUrls: ["./fxrates-popup.component.scss"],
})
export class FxratesPopupComponent implements OnInit, OnChanges {
  @Output() submitForm = new EventEmitter<any>();
  @Input() selectedFxSource: string;
  @Input() fxOptions: any = null;
  transformCurrencySelection(selectedValues: any[], allOptions: any[]): any[] {
    if (!allOptions || !allOptions.length) return [];
    // Extract all value strings from selectedValues array of objects or strings
    const selectedValueStrings = selectedValues.map(v => v.value || v);
    return allOptions
      .map(parent => {
        const isParentSelected = selectedValueStrings.includes(parent.value);
        const selectedChildren = parent.children
          ? parent.children.filter(child => selectedValueStrings.includes(child.value))
          : [];
        if (isParentSelected) {
          // If parent is selected, include all its children
          return {
            value: parent.value,
            label: parent.label,
            children: parent.children
              ? parent.children.map(child => ({
                  value: child.value,
                  label: child.label
                }))
              : []
          };
        } else if (selectedChildren.length > 0) {
          // If only some children are selected, include only those
          return {
            value: parent.value,
            label: parent.label,
            children: selectedChildren.map(child => ({
              value: child.value,
              label: child.label
            }))
          };
        }
        return null;
      })
      .filter(Boolean);
  }
  isPeriodRangeSelected(): boolean {
    const selected = this.periodOptions.find(o => o.periodId === this.form.get(PERIOD_FORM_CONTROL)?.value);
    return selected?.periodName === PERIOD_RANGE;
  }
  getPeriodComboClass() {
    const selected = this.periodOptions.find(o => o.periodId === this.form.get(PERIOD_FORM_CONTROL)?.value);
    return {
      'k-custom-solid-dropdown': true,
      'k-select-medium': true,
      'k-dropdown-width-260': selected?.periodName === PERIOD_RANGE
    };
  }
  periodOptions: any[] = [];
  frequencyOptions: any[] = [];
  financialYearEndOptions: any[] = [];
  currencySelectionOptions: any[] = [];
  fxMethodologyOptions: any[] = [];
  form: FormGroup;
  isFormChanged: boolean = false;

  // Select All Currency state tracking - using getters for real-time calculation
  get isAllCurrenciesSelected(): boolean {
    if (!this.currencySelectionOptions.length || !this.form) return false;
    
    const selectedCurrencies = this.form.get(CURRENCY_SELECTION_FORM_CONTROL)?.value || [];
    const allAvailableCurrencyValues = this.getAllAvailableCurrencyValues();
    const selectedCurrencyValues = selectedCurrencies.map(item => item?.value || item);
    const selectedCount = allAvailableCurrencyValues.filter(value => 
      selectedCurrencyValues.includes(value)
    ).length;
    
    return selectedCount > 0 && selectedCount === allAvailableCurrencyValues.length;
  }

  get isCurrencySelectionIndeterminate(): boolean {
    if (!this.currencySelectionOptions.length || !this.form) return false;
    
    const selectedCurrencies = this.form.get(CURRENCY_SELECTION_FORM_CONTROL)?.value || [];
    const allAvailableCurrencyValues = this.getAllAvailableCurrencyValues();
    const selectedCurrencyValues = selectedCurrencies.map(item => item?.value || item);
    const selectedCount = allAvailableCurrencyValues.filter(value => 
      selectedCurrencyValues.includes(value)
    ).length;
    
    return selectedCount > 0 && selectedCount < allAvailableCurrencyValues.length;
  }

  minDate: Date = new Date(2010, 0, 1);
  maxDate: Date = new Date(new Date().getFullYear() + 5, new Date().getMonth(), new Date().getDate());

  constructor(private currencyService: CurrencyService, private fb: FormBuilder, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.form = this.fb.group({
      [PERIOD_FORM_CONTROL]: ['', Validators.required],
      [FROM_DATE_FORM_CONTROL]: [''],
      [TO_DATE_FORM_CONTROL]: [''],
      [FREQUENCY_FORM_CONTROL]: [[], Validators.required],
      [FINANCIAL_YEAR_END_FORM_CONTROL]: [[], Validators.required],
      [CURRENCY_SELECTION_FORM_CONTROL]: [[], Validators.required],
      [FX_METHODOLOGY_FORM_CONTROL]: [[], Validators.required]
    });
    this.getFxRateOptions(false);
    // Toggle validators for date range based on period
    this.form.get(PERIOD_FORM_CONTROL)?.valueChanges.subscribe((value) => {
      const fromCtrl = this.form.get(FROM_DATE_FORM_CONTROL);
      const toCtrl = this.form.get(TO_DATE_FORM_CONTROL);
      if (value === PERIOD_RANGE) {
        fromCtrl?.setValidators([Validators.required]);
        toCtrl?.setValidators([Validators.required]);
      } else {
        fromCtrl?.setValue('');
        toCtrl?.setValue('');
        fromCtrl?.clearValidators();
        toCtrl?.clearValidators();
      }
      fromCtrl?.updateValueAndValidity();
      toCtrl?.updateValueAndValidity();
    });

    // Listen for frequency changes to toggle Financial Year End
    this.form.get(FREQUENCY_FORM_CONTROL)?.valueChanges.subscribe((values: any[]) => {
      const disableFyEnd = values.some((v: any) => {
        if (typeof v === 'string') {
          return FREQUENCY_NAMES.includes(v);
        }
        if (v && v.frequencyName) {
          return FREQUENCY_NAMES.includes(v.frequencyName);
        }
        return false;
      });
      const fyEndControl = this.form.get(FINANCIAL_YEAR_END_FORM_CONTROL);
      if (fyEndControl) {
        if (disableFyEnd) {
          fyEndControl.clearValidators();
          fyEndControl.setValue([]);
          fyEndControl.updateValueAndValidity();
          fyEndControl.markAsPristine();
          fyEndControl.markAsUntouched();
          fyEndControl.disable({ emitEvent: false });
        } else {
          fyEndControl.enable({ emitEvent: false });
          fyEndControl.setValidators([Validators.required]);
          fyEndControl.updateValueAndValidity();
        }
      }
    });

    // Listen for currency selection changes for change detection
    this.form.get(CURRENCY_SELECTION_FORM_CONTROL)?.valueChanges.subscribe(() => {
      this.cdr.detectChanges();
    });
  }
  getFxRateOptions(isBulkUpload: boolean) {
    this.periodOptions = this.fxOptions?.fxRatePeriodModels || [];
    this.frequencyOptions = this.fxOptions?.fxRateFrequencyModels || [];
    this.financialYearEndOptions = this.fxOptions?.fyEndModels || [];
    this.currencySelectionOptions = this.fxOptions?.fxRateCurrencyModels || [];
    this.fxMethodologyOptions = this.fxOptions?.fxRateMethodologyModels || [];
    
    // Set default values from fxOptions
    this.setDefaultValues();
  }

  setDefaultValues(): void {
    if (!this.fxOptions || !this.form) return;

    // Set default period (first available option) - using periodName as valueField
    if (this.periodOptions.length > 0) {
      this.form.patchValue({
        [PERIOD_FORM_CONTROL]: this.periodOptions[0].periodName
      });
    }

    // Set default frequency (first available option) - using frequencyId as valueField
    if (this.frequencyOptions.length > 0) {
      this.form.patchValue({
        [FREQUENCY_FORM_CONTROL]: [this.frequencyOptions[3]]
      });
    }

    // Set default financial year end (first available option) - using month as valueField
    if (this.financialYearEndOptions.length > 0) {
      this.form.patchValue({
        [FINANCIAL_YEAR_END_FORM_CONTROL]: [this.financialYearEndOptions[11]]
      });
    }

          // Set default currency selection - select all available currencies (parents and children)
      if (this.currencySelectionOptions.length > 0) {
        const allCurrencyObjects: any[] = [];
        
        this.currencySelectionOptions.forEach(currency => {
          if (currency.children && currency.children.length > 0) {
            // Add the parent currency object first
            allCurrencyObjects.push(currency);
            // Then add all child objects
            currency.children.forEach(child => {
              if (child.value) {
                allCurrencyObjects.push(child);
              }
            });
          } else if (currency.value) {
            // If no children, add the parent currency object
            allCurrencyObjects.push(currency);
          }
        });
        
        this.form.patchValue({
          [CURRENCY_SELECTION_FORM_CONTROL]: allCurrencyObjects
        });
      }
  
      // Set default FX methodology (first available option) - using methodologyId as valueField
    if (this.fxMethodologyOptions.length > 0) {
      this.form.patchValue({
        [FX_METHODOLOGY_FORM_CONTROL]: [this.fxMethodologyOptions[0]]
      });
    }
  }

  onFormChange(): void {
    this.isFormChanged = true;
  }

  onCurrencyValueChange(value: any): void {
    this.onFormChange();
  }

  // Select All Currency functionality
  onSelectAllCurrencies(event: any): void {
    if (this.isAllCurrenciesSelected || this.isCurrencySelectionIndeterminate) {
      // Deselect all
      this.form.patchValue({
        [CURRENCY_SELECTION_FORM_CONTROL]: []
      });
    } else {
      // Select all
      const allCurrencyObjects: any[] = [];
      
      this.currencySelectionOptions.forEach(currency => {
        if (currency.children && currency.children.length > 0) {
          // Add the parent currency object first
          allCurrencyObjects.push(currency);
          // Then add all child objects
          currency.children.forEach(child => {
            if (child.value) {
              allCurrencyObjects.push(child);
            }
          });
        } else if (currency.value) {
          // If no children, add the parent currency object
          allCurrencyObjects.push(currency);
        }
      });
      
      this.form.patchValue({
        [CURRENCY_SELECTION_FORM_CONTROL]: allCurrencyObjects
      });
    }
    
    this.onFormChange();
  }

  getAllAvailableCurrencyValues(): string[] {
    const allValues: string[] = [];
    
    this.currencySelectionOptions.forEach(currency => {
      if (currency.children && currency.children.length > 0) {
        // Add parent value
        if (currency.value) {
          allValues.push(currency.value);
        }
        // Add all child values
        currency.children.forEach(child => {
          if (child.value) {
            allValues.push(child.value);
          }
        });
      } else if (currency.value) {
        // If no children, add the parent currency value
        allValues.push(currency.value);
      }
    });
    
    return allValues;
  }

  getTotalCurrencyCount(): number {
    let total = 0;
    this.currencySelectionOptions.forEach(currency => {
      if (currency.children && currency.children.length > 0) {
        total += 1 + currency.children.length; // Parent + children
      } else {
        total += 1;
      }
    });
    return total;
  }

  onSubmit(): void {
    if (this.form.valid) {
      const selectedModels = this.transformCurrencySelection(this.form.value.currencySelection, this.currencySelectionOptions);
      const submitData = { ...this.form.value, fxRateCurrencyModels: selectedModels };
      this.submitForm.emit(submitData);
      this.isFormChanged = false;
    }
  }

  onCancel(): void {
    // Reset all fields and form state
    this.form.reset({
      [PERIOD_FORM_CONTROL]: '',
      [FROM_DATE_FORM_CONTROL]: '',
      [TO_DATE_FORM_CONTROL]: '',
      [FREQUENCY_FORM_CONTROL]: [],
      [FINANCIAL_YEAR_END_FORM_CONTROL]: [],
      [CURRENCY_SELECTION_FORM_CONTROL]: [],
      [FX_METHODOLOGY_FORM_CONTROL]: []
    });
    this.isFormChanged = false;
  }

  // Kendo MultiSelectTree helpers
  hasChildren = (item: any): boolean => {
    return Array.isArray(item.children) && item.children.length > 0;
  };

  fetchChildren = (item: any): any[] => {
    return item.children || [];
  };
  tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags];
  }
   ngOnChanges(changes: SimpleChanges): void { 
    if(changes['fxOptions'] && this.fxOptions)
    {
      this.getFxRateOptions(false);
    }
   }
}
