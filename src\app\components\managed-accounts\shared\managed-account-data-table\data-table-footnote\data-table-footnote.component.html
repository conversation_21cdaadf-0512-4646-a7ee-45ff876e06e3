<div class="row mr-0 ml-0 clo-table-content">
    <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 Heading2-M">
    <div class="row mr-0 ml-0 clo-table-content">
      <ng-container *ngFor="let footnote of footnotes">
        <div class="custom-footnote col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pt-3 pb-3 pr-3 pl-3 clo-item TextTruncate"
          [ngClass]="footnote.isExpanded ? 'clo-active':'clo-in-active'">
          <div title="{{footnote.name}}" class="float-left TextTruncate Heading2-M">{{footnote.name}}</div>
          <div class="float-right">
            <span *ngIf="footnote.isExpanded" id="edit-footnote" (click)="toggleEdit(footnote)" (keypress)="toggleEdit(footnote)">
              <img class="custom-size" alt="" src="assets/dist/images/clo_edit.svg" />
            </span>
            <a (click)="expandPanel(footnote)">
              <img src="assets/dist/images/{{footnote.isExpanded ? 'arrow-down.svg' :'chevron-down-i.svg'}}"
                alt="Sort left" />
            </a>
          </div>
        </div>
        <ng-container *ngIf="footnote.isExpanded">
          <div class="col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 child-add-item TextTruncate">
            <div class="textarea-container">
              <ng-container *ngIf="!footnote.isEdit">
                <div>
                  <ng-container *ngIf="footnote.newComment; else noComment">
                    <app-custom-quill-editor [readOnly]="true" class="custom-quill-editor"  [noteText]="footnote.newComment" [ngModel]="footnote.newComment" [modules]="quillConfig"></app-custom-quill-editor>
                  </ng-container>
                  <ng-template #noComment>
                    <div class="empty-text Body-R pt-2 pl-3">N/A</div>
                  </ng-template>
                </div>
              </ng-container>
              <ng-container *ngIf="footnote.isEdit">
                <app-custom-quill-editor [editorPlaceholder]="editorPlaceholder" [(ngModel)]="footnote.newComment"  [noteText]="footnote.newComment"  [showCharCount]="true" class="custom-quill-editor"></app-custom-quill-editor>
                <div class="d-flex justify-content-between custom-quillcontainer pb-3 pl-4 pr-4 pt-3">
                  <button id="btn-reset" class="btn TextTruncate btn-warning mr-2 TextTruncate" (click)="onReset(footnote)">Clear</button>
                  <div class="btn-controls float-right">
                    <button id="btn-cancel" (click)="onCancel(footnote)" class="btn TextTruncate btn-warning mr-2 TextTruncate">Cancel</button>
                    <button id="btn-save" class="btn-save-clo btn btn-primary" (click)="onSave(footnote)">Save</button>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
  </div>