import { ViewManagedAccountDetailsComponent } from './view-managed-account-details.component';
import { ManagedAccountsCommentaryWrapperComponent } from './commentary-wrapper/commentary-wrapper.component';
import { ManagedAccountsCommentaryComponent } from './commentary/commentary.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from 'src/app/custom-modules/material.module';
import { PrimeNgModule } from 'src/app/custom-modules/prime-ng.module';
import { AngularResizeEventModule } from 'angular-resize-event';
import { QuillModule } from 'ngx-quill';
import { SharedDirectiveModule } from 'src/app/directives/shared-directive.module';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { SharedComponentModule } from 'src/app/custom-modules/shared-component.module';
import { SharedCloModule } from 'src/app/components/clo/shared-clo.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { InvestmentPageComponent } from './investment-page/investment-page.component';
import { TrackRecordComponent } from './track-record/track-record.component';
import { PortfolioStatisticsComponent } from './portfolio-statistics/portfolio-statistics.component';
import { InvestmentPortfolioComponent } from './investment-portfolio/investment-portfolio.component';
import { InvestorCashflowActivityComponent } from './investor-cashflow-activity/investor-cashflow-activity.component';
import { SafeHtmlPipe } from '../shared/safe-html.pipe';
import { ManagedAccountDataTableComponent } from '../shared/managed-account-data-table/managed-account-data-table.component';
import { DataTableFootnoteComponent } from '../shared/managed-account-data-table/data-table-footnote/data-table-footnote.component';
import { StaticDataTableComponent } from '../shared/managed-account-data-table/static-data-table/static-data-table.component';
import { FlatDataTableComponent } from '../shared/managed-account-data-table/flat-data-table/flat-data-table.component';
import { UploadDataModalComponent } from '../../shared/upload-data-modal/upload-data-modal.component';

@NgModule({
  declarations: [
    ViewManagedAccountDetailsComponent,
    ManagedAccountsCommentaryWrapperComponent,
    ManagedAccountsCommentaryComponent,
    SafeHtmlPipe,
    InvestmentPageComponent,
    TrackRecordComponent,
    PortfolioStatisticsComponent,
    InvestmentPortfolioComponent,
    InvestorCashflowActivityComponent,
    ManagedAccountDataTableComponent,
    DataTableFootnoteComponent,
    StaticDataTableComponent,
    FlatDataTableComponent,
    UploadDataModalComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    SharedComponentModule,
    MaterialModule,
    PrimeNgModule,
    AngularResizeEventModule,
    SharedDirectiveModule,
    QuillModule,
    RouterModule.forChild([
      { path: '', component: ViewManagedAccountDetailsComponent }
    ]),
    KendoModule,
    SharedCloModule,
    NgbModule
  ],
  exports: [
    ViewManagedAccountDetailsComponent,
    ManagedAccountsCommentaryWrapperComponent,
    ManagedAccountsCommentaryComponent,
    SafeHtmlPipe

  ],
  providers: []
})
export class ViewManagedAccountDetailsComponentModule { }
