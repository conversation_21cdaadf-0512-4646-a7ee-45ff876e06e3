import * as _ from 'lodash';

export interface INavigationTabLink {
    name: string;
    aliasName: string;
    tableName: string;
    isSelected: boolean;
    moduleId?: number;
    children?: INavigationTabLink[];
    isStaticTable?: boolean;
}


export class NavigationTabLink implements INavigationTabLink {
    name: string;
    aliasName: string;
    tableName: string;
    isSelected: boolean;
    children?: INavigationTabLink[];
    isStaticTable?: boolean = false;
    constructor(data: INavigationTabLink) {
        if (!data) {
            return;
        }
        _.assign(this, data);
        this.isSelected = false;
        if (data.children) {
            this.children = data.children.map(child => new NavigationTabLink(child));
        }
    }
}


