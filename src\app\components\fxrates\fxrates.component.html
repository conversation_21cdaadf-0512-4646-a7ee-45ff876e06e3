<div class="row performance-section mr-0 ml-0" *ngIf="isEnableView">
    <div class="col-12 col-sm-12 col-md-12 col-xl-12 col-lg-12 outer-section pl-0 pr-0">
        <div class="portfolio-company-table">
            <div class="border-bottom pt-1 pb-1">
                <div class="row mr-0 ml-0">
                    <div class=" col-lg-12 col-md-12 col-sm-12 pl-0 pr-0">
                        <div class="pull-right headerfontsize">
                            <div class="d-inline-block search pr-2">
                                <kendo-textbox [clearButton]="false" [fillMode]="'solid'" [size]="'medium'"
                                    placeholder="Search" class="k-custom-solid-dropdown k-dropdown-height-32"
                                    [(ngModel)]="searchText" (valueChange)="onFilter($event)">
                                    <ng-template kendoTextBoxPrefixTemplate>
                                        <span class="fa fa-search fasearchicon"></span>
                                    </ng-template>
                                </kendo-textbox>
                            </div>
                            <div class="d-inline-block">
                                <div class="d-inline-block pl-1">
                                    <kendo-combobox [data]="fxSource" textField="source" valueField="source"
                                        [fillMode]="'solid'" [valuePrimitive]="true" [clearButton]="false"
                                        [size]="'medium'" [placeholder]="'Select here...'"
                                        class="k-dropdown-width-200 k-custom-solid-dropdown k-dropdown-height-32"
                                        [(ngModel)]="selectedFxSource" (valueChange)="onFxSourceChange($event)">
                                    </kendo-combobox>
                                </div>
                            </div>
                            <div class="d-inline-block cloud_download pl-0" id="div-download-fxrates">
                                <div class="d-inline  p-2 headerfontsize icon-menu mr-2 ml-2"><img id="dropdownMenuButton"
                                        [matMenuTriggerFor]="menu" src="assets/dist/images/FiSliders.svg"
                                        class="cursor-filter" alt="" #masterMenuTrigger="matMenuTrigger" />
                                    <span [matMenuTriggerFor]="menu" #filterMenuTrigger="matMenuTrigger"
                                        class=""></span>
                                </div>
                                <div class="d-inline-block">
                                    <button class="kendo-custom-button Body-R apply-btn" [disabled]="!fxrateslistResults.length" kendoButton
                                        themeColor="primary" (click)="downloadFxRates()">Download</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="">
                <kendo-grid
                    class="k-grid-border-right-width k-grid-border-bottom-width k-grid-outline-none custom-kendo-fxrates-table-grid"
                    [kendoGridBinding]="fxrateslistResults" [scrollable]="true"  [resizable]="true" [sortable]="true"
                    >
                    <!-- Basic columns (always shown) -->
                    <kendo-grid-column field="fromCurrencyCode" title="From Currency" [width]="180" [sticky]="true"
                        [minResizableWidth]="160" [maxResizableWidth]="300">
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="content">
                                <span>{{rowData.fromCurrencyCode}}</span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column field="toCurrencyCode" title="To Currency" [width]="180" [sticky]="true"
                        [minResizableWidth]="160" [maxResizableWidth]="300">
                        <ng-template kendoGridCellTemplate let-rowData>
                            <div class="content">
                                <span>{{rowData.toCurrencyCode}}</span>
                            </div>
                        </ng-template>
                    </kendo-grid-column>

                    <!-- Dynamic period columns -->
                    <ng-container *ngFor="let period of fxratePeriods">
                        <kendo-grid-column-group [title]="period">
                            <ng-container *ngFor="let methodology of selectedMethodologies">
                                <kendo-grid-column [field]="sanitizeFieldId(period, methodology)" [title]="methodology"
                                    [width]="200" [minResizableWidth]="300" [maxResizableWidth]="400">
                                    <ng-template kendoGridCellTemplate let-rowData>
                                        <span tabindex="0" class="prtcmny-det-o">
                                            <span class="content">
                                                <span
                                                    [title]="getValueForMethodology(rowData, period, methodology) | minusSignToBrackets">
                                                    {{getValueForMethodology(rowData, period, methodology) |
                                                    minusSignToBrackets}}
                                                </span>
                                            </span>
                                        </span>
                                    </ng-template>
                                </kendo-grid-column>
                            </ng-container>
                        </kendo-grid-column-group>
                    </ng-container>

                    <ng-template kendoGridNoRecordsTemplate>
                        <app-empty-state class="finacials-beta-empty-state" [imageHeight]="'calc(100vh - 210px)'"
                            [isGraphImage]="false"></app-empty-state>
                    </ng-template>
                </kendo-grid>
            </div>
        </div>
    </div>
</div>
<mat-menu #menu="matMenu" [hasBackdrop]="true" matMenuTriggerRestoreFocus="false" class="fixed-fxrates-menu">
    <app-fxrates-popup [selectedFxSource]="selectedFxSource" [fxOptions]="fxOptions"
        (submitForm)="onFxRatesFormSubmit($event)"></app-fxrates-popup>
</mat-menu>
<app-loader-component *ngIf="isLoader"></app-loader-component>