import { Component, Input, OnInit, OnD<PERSON>roy, OnChanges, SimpleChanges } from '@angular/core';
import { INavigationTabLink } from '../../shared/navigationLink.model';
import { ManagedAccountService } from '../../managed-account.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Commentary as CommentaryPayload, CommentaryUI } from '../../models/commentary.model';
import { ManagedAccountConstants } from 'src/app/common/constants';
import { ManagedAccountPermissionConstants } from 'src/app/common/constants';

@Component({
  selector: 'app-portfolio-statistics',
  templateUrl: './portfolio-statistics.component.html',
  styleUrls: ['./portfolio-statistics.component.scss']
})
export class PortfolioStatisticsComponent implements OnInit, OnDestroy, OnChanges {
  @Input() permissions: any;
  @Input() accountId: string;
  @Input() subPagefieldList: any[];
  public isExpanded: boolean = false;
  public selectedTab: string = 'Portfolio_Statistics_Limits';

  // Commentary functionality properties
  public commentary: CommentaryUI | null = null;

  public investmentLimitsConstant = ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits;
  private subscription = new Subscription();
  canViewCommentary: boolean = false;
  canEditCommentary: boolean = false;

  public nestedNavigationLinks: INavigationTabLink[] = [];
  public filteredNavigationLinks: INavigationTabLink[] = [];

  constructor(
    private toastrService: ToastrService,
    private managedAccountService: ManagedAccountService,
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['subPagefieldList'] && changes['subPagefieldList'].currentValue) {
      this.buildNestedNavigationLinks();
      this.initializeCommentary();
    }
  }

  private initializeCommentary(): void {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      const investmentLimitsItem = this.subPagefieldList.find(item => 
        item.name === ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits
      );

      if (investmentLimitsItem) {
        this.commentary = {
          tableId: 26,
          id: 0,
          sequenceNo: 1,
          name: investmentLimitsItem.displayName || investmentLimitsItem.aliasName,
          newComment: '',
          commentaryType: ManagedAccountConstants.COMMENTARY_TYPES.Investment_Limits, 
          isExpanded: false,
          isEdit: false
        };

        // Load commentary if accountId is available
        if (this.accountId) {
          this.loadPortfolioCommentary();
        }
      }
    }
  }

  private buildNestedNavigationLinks() {
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {      
      const portfolioStatsTabs = this.subPagefieldList.filter(item => 
        [ManagedAccountPermissionConstants.ManagedAccountSubFeature.PortfolioStatistics,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Sectors,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Issuers,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetRatings,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetClasses,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.Currencies,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.Geographies,
          ManagedAccountPermissionConstants.ManagedAccountSubFeature.Sectors].includes(item.name)
      );
      this.nestedNavigationLinks = this.groupTabsIntoNestedStructure(portfolioStatsTabs);
    }
  }

  private groupTabsIntoNestedStructure(tabs: any[]): INavigationTabLink[] {
    return [
      {
        name: 'Portfolio_Statistics_Limits',
        aliasName: 'Portfolio Statistics and limits',
        tableName: 'Portfolio Statistics and limits',
        isSelected: false,
        children: tabs.filter(tab => [ManagedAccountPermissionConstants.ManagedAccountSubFeature.PortfolioStatistics, ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits].includes(tab.name))
          .map(tab => ({
            name: tab.name,
            aliasName: tab.displayName || tab.aliasName,
            tableName: tab.name,
            isSelected: false
          }))
      },
      {
        name: 'Top_5_Sectors_Issuers',
        aliasName: 'Top 5 Sectors and Issuers',
        tableName: 'Top 5 Sectors and Issuers',
        isSelected: false,
        children: tabs.filter(tab => [ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Sectors, ManagedAccountPermissionConstants.ManagedAccountSubFeature.Top5Issuers].includes(tab.name))
          .map(tab => ({
            name: tab.name,
            aliasName: tab.displayName || tab.aliasName,
            tableName: tab.name,
            isSelected: false
          }))
      },
      {
        name: 'Managed_Account_Composition',
        aliasName: 'Managed Account Composition',
        tableName: 'Managed Account Composition',
        isSelected: false,
        children: tabs.filter(tab => [ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetRatings, ManagedAccountPermissionConstants.ManagedAccountSubFeature.AssetClasses, ManagedAccountPermissionConstants.ManagedAccountSubFeature.Currencies, ManagedAccountPermissionConstants.ManagedAccountSubFeature.Geographies, ManagedAccountPermissionConstants.ManagedAccountSubFeature.Sectors].includes(tab.name))
          .map(tab => ({
            name: tab.name,
            aliasName: tab.displayName || tab.aliasName,
            tableName: tab.name,
            isSelected: false
          }))
      }
    ].filter(group => group.children && group.children.length > 0);
  }

  ngOnInit() {
    this.loadPermissions();
    
    // Check if subPagefieldList is already available
    if (this.subPagefieldList && this.subPagefieldList.length > 0) {
      this.buildNestedNavigationLinks();
      this.initializeCommentary();
    }

    this.filteredNavigationLinks = this.nestedNavigationLinks
      .map(link => {
        const filteredChildren = link.children?.filter(child => this.canView(child.tableName));
        if (filteredChildren && filteredChildren.length > 0) {
          return { ...link, children: filteredChildren };
        }
        return null;
      })
      .filter(link => link !== null) as INavigationTabLink[];
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private loadPermissions(): void {
    // Set permissions based on actual permissions - can be expanded later
    this.canViewCommentary = this.permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits]?.canView || false;
    this.canEditCommentary = this.permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentLimits]?.canEdit || false;
  }

  loadPortfolioCommentary(): void {
    if (!this.accountId || !this.commentary) {
      this.toastrService.error('Account ID is required.', "", { positionClass: "toast-center-center" });
      return;
    }

    this.subscription.add(
      this.managedAccountService.getCommentariesByManagedAccount(this.accountId).subscribe({
        next: (response) => {
          this.processCommentaryResponse(response);
        },
        error: (error) => {
          console.error('Error loading commentary:', error);
          this.toastrService.error('Failed to load commentary.', "", { positionClass: "toast-center-center" });
        }
      })
    );
  }

  private processCommentaryResponse(response: any[]): void {
    if (!this.commentary) return;

    // Reset commentary to default state
    this.commentary.newComment = '';
    this.commentary.id = 0;
    this.commentary.isExpanded = false;

    // Process the array response and filter by commentaryType = 3
    if (response && Array.isArray(response)) {
      const portfolioCommentaryData = response.find(commentary => commentary.commentaryType === ManagedAccountConstants.COMMENTARY_TYPES.Investment_Limits);      
      
      if (portfolioCommentaryData && portfolioCommentaryData.commentaryText) {
        this.commentary.newComment = portfolioCommentaryData.commentaryText;
        this.commentary.id = portfolioCommentaryData.commentaryID;
        
        // Auto-expand if content exists
        if (!this.isEmpty(this.commentary.newComment)) {
          this.commentary.isExpanded = true;
        }
      }
    }
  }

  onCommentaryUpdated(updatedCommentary: CommentaryUI): void {
    this.commentary = { ...updatedCommentary };
    this.saveCommentary(updatedCommentary);
  }

  onToggleExpansion(commentary: CommentaryUI): void {
    this.commentary.isExpanded = !this.commentary.isExpanded;
  }

  onEditToggled(commentary: CommentaryUI): void {
    this.commentary.isEdit = commentary.isEdit;
  }

  private saveCommentary(commentary: CommentaryUI): void {
    const commentaryData = this.mapToCommentaryModel(commentary, this.accountId);

    this.managedAccountService.saveCommentaries(commentaryData).subscribe(
      (data) => {
        if (data.commentaryId) {
          this.commentary.id = data.commentaryId;
          this.toastrService.success(data.message, "", { positionClass: "toast-center-center" });
          this.commentary.isEdit = false; // Exit edit mode after successful save
        }
      },
      (error) => {
        let errorMessage = 'Failed to save commentary.';

        if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        console.error('Error saving commentary:', error);
        this.toastrService.error(errorMessage, "", { positionClass: "toast-center-center" });
      }
    );
  }

  private mapToCommentaryModel(commentary: CommentaryUI, managedAccountId: string): CommentaryPayload {
    let commentaryText = commentary.newComment || '';

    return {
      commentaryID: commentary.id || 0,
      commentaryText: commentaryText,
      commentaryType: commentary.commentaryType,
      managedAccountID: managedAccountId
    };
  }
  
  private isEmpty(val: any): boolean {
    val = val?.replace(/<.*?>/g, '');
    return (val === undefined || val == null || val.length <= 0) ? true : false;
  }

  canView(tableName: string): boolean {
    return this.permissions?.[tableName]?.canView;
  }

  toggleEdit(event: Event) {
    event.stopPropagation();
    this.isExpanded = !this.isExpanded;
  }
}

