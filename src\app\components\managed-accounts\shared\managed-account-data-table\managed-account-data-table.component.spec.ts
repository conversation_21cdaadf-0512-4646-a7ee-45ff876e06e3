import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, Input } from '@angular/core';
import { ManagedAccountDataTableComponent } from './managed-account-data-table.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

// Mock child component
@Component({
  selector: 'app-data-table-footnote',
  template: '<div>Mock Data Table Footnote</div>'
})
class MockDataTableFootnoteComponent {
  @Input() footnotes: any[];
}

describe('ManagedAccountDataTableComponent', () => {
  let component: ManagedAccountDataTableComponent;
  let fixture: ComponentFixture<ManagedAccountDataTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        ManagedAccountDataTableComponent,
        MockDataTableFootnoteComponent
      ],
      imports: [
        KendoModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot(),
        HttpClientTestingModule
      ],
      providers: [
        NgbModal
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.tableTitle).toBe('');
    expect(component.data).toEqual([]);
    expect(component.tableName).toBe('');
    expect(component.footnotes).toBeDefined();
    expect(component.footnotes.length).toBe(1);
  });

  it('should accept input properties', () => {
    const testTitle = 'Test Table';
    const testData = [{ id: 1, name: 'Test' }];
    const testName = 'test_table';
    
    component.tableTitle = testTitle;
    component.data = testData;
    component.tableName = testName;
    
    expect(component.tableTitle).toBe(testTitle);
    expect(component.data).toEqual(testData);
    expect(component.tableName).toBe(testName);
  });
});
