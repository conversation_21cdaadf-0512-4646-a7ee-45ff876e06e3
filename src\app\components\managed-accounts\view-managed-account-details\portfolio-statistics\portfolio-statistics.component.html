<div class="portfolio-statistics-container">
  <div class="tab-content">
    <kendo-tabstrip id="{{selectedTab}}">
      <ng-container *ngFor="let tab of filteredNavigationLinks">
        <kendo-tabstrip-tab [title]="tab.aliasName" [selected]="filteredNavigationLinks[0] === tab">
          <ng-template kendoTabContent>
            <div *ngIf="tab.children && tab.children.length > 0">
              <ng-container *ngFor="let child of tab.children">
                <div class="data-table-container" *ngIf="child.name !== investmentLimitsConstant">
                  <div class="table-wrapper">
                    <app-managed-account-data-table [tableTitle]='child.aliasName' [data]=''
                      [tableName]='child.tableName' [permissions]="permissions">
                    </app-managed-account-data-table>
                  </div>
                </div>
              </ng-container>
            </div>
          </ng-template>
        </kendo-tabstrip-tab>
      </ng-container>
    </kendo-tabstrip>
  </div>

  <!-- Investment Limits Container -->
  <div class="commentary-section" *ngIf="commentary && canViewCommentary">
    <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 p-0 Heading2-M">
      <app-managed-accounts-commentary 
        [commentary]="commentary" 
        [canEdit]="canEditCommentary"
        [canView]="canViewCommentary" 
        (commentaryUpdated)="onCommentaryUpdated($event)"
        (toggleExpansion)="onToggleExpansion($event)" 
        (editToggled)="onEditToggled($event)">
      </app-managed-accounts-commentary>
    </div>
  </div>
</div>