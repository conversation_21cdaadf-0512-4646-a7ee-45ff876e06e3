import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DataTableFootnoteComponent } from './data-table-footnote.component';
import { ToastrService } from 'ngx-toastr';

// Mock ToastrService
class MockToastrService {
  success() {}
  error() {}
  warning() {}
  info() {}
}

describe('DataTableFootnoteComponent', () => {
  let component: DataTableFootnoteComponent;
  let fixture: ComponentFixture<DataTableFootnoteComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DataTableFootnoteComponent],
      providers: [
        { provide: ToastrService, useClass: MockToastrService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataTableFootnoteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.commentarylist).toBeUndefined();
    expect(component.footnotes).toBeUndefined();
    expect(component.quillConfig).toBeUndefined();
    expect(component.editorPlaceholder).toBeUndefined();
  });

  it('should toggle edit mode', () => {
    const testItem = { isEdit: false };
    component.toggleEdit(testItem);
    expect(testItem.isEdit).toBe(true);
    
    component.toggleEdit(testItem);
    expect(testItem.isEdit).toBe(false);
  });

  it('should expand panel and collapse others', () => {
    const testItems = [
      { isExpanded: false, isEdit: false },
      { isExpanded: true, isEdit: true },
      { isExpanded: false, isEdit: false }
    ];
    
    component.footnotes = testItems;
    component.expandPanel(testItems[0]);
    
    expect(testItems[0].isExpanded).toBe(true);
    expect(testItems[1].isExpanded).toBe(false);
    expect(testItems[1].isEdit).toBe(false);
    expect(testItems[2].isExpanded).toBe(false);
  });

  it('should emit save event', () => {
    const testItem = { id: 1, name: 'Test' };
    spyOn(component.save, 'emit');
    
    component.onSave(testItem);
    
    expect(component.save.emit).toHaveBeenCalledWith(testItem);
  });

  it('should emit cancel event', () => {
    const testItem = { id: 1, name: 'Test' };
    spyOn(component.cancel, 'emit');
    
    component.onCancel(testItem);
    
    expect(component.cancel.emit).toHaveBeenCalledWith(testItem);
  });

  it('should emit reset event and clear newComment', () => {
    const testItem = { id: 1, name: 'Test', newComment: 'Some comment' };
    spyOn(component.reset, 'emit');
    
    component.onReset(testItem);
    
    expect(testItem.newComment).toBe('');
    expect(component.reset.emit).toHaveBeenCalledWith(testItem);
  });
});
