import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-data-table-footnote',
  templateUrl: './data-table-footnote.component.html',
  styleUrls: ['./data-table-footnote.component.scss']
})
export class DataTableFootnoteComponent {
  constructor(private toastrService: ToastrService) {}
 
  @Input() commentarylist: any[];
  @Input() footnotes: any[];
  @Input() quillConfig: any;
  @Input() editorPlaceholder: string;
  @Output() save = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<any>();
  @Output() reset = new EventEmitter<any>();

  toggleEdit(footnote: any) {
    footnote.isEdit = !footnote.isEdit;
  }

  expandPanel(footnote: any) {
    this.footnotes.forEach(item => {
      if (item !== footnote) {
        item.isExpanded = false;
        item.isEdit = false;
      }
    });
    footnote.isExpanded = !footnote.isExpanded;
  }
  onSave(footnote: any) {
    this.save.emit(footnote);
  }

  onCancel(footnote: any): void {
   this.cancel.emit(footnote);
  }

  onReset(footnote: any): void {
    footnote.newComment = '';
    this.reset.emit(footnote);
  }
}
