export interface Commentary {
  commentaryID?: number;
  commentaryText?: string;
  commentaryType: number;
  managedAccountID: string;
  createdBy?: number;
}

export interface CommentaryUI {
  tableId: number;
  sequenceNo: number;
  id: number;
  name: string;
  newComment: string;
  commentaryType: number;
  isExpanded: boolean;
  isEdit: boolean;
}

export interface DownloadTemplate {
  managedAccountId: string;
  moduleName: string;
}
