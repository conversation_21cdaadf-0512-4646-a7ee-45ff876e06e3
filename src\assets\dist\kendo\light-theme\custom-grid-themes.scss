@import '../../../../_variables';
@import '../../../../assets/dist/css/font';
$border-color: #dee2e6;
$font-family-medium: Helvetica Neue LT W05_65 Medium, Arial, Verdana, Tahoma, sans-serif;

.k-grid .k-table-row.k-table-alt-row {
  background-color: transparent;
}

.k-grid td {
  border-bottom: 1px solid $border-color;
}

.k-grid .k-grid-header {
  border-color: $border-color;
}

kendo-grid.k-grid .k-grid-content-sticky,
.k-grid-header-sticky,
.k-grid-header .k-grid-header-sticky,
.k-grid .k-grid-header-locked,
.k-grid .k-grid-content-locked,
.k-grid .k-grid-header-locked .k-table-th,
.k-grid .k-grid-content-locked td,
.k-grid .k-grid-content-locked .k-table-td {
  border-color: $border-color !important;
}

.k-grid .k-grid-header .k-table-th {
  .k-column-title {
    white-space: nowrap;
    color: $content-paragraph;
    @extend .Body-M;
  }

  .k-link {
    padding-block: 0.75rem;
    padding-inline: 1rem;
  }
}

.k-grid-norecords {
  .k-table-td {
    border: none !important;
  }
}

.k-grid-outline-none {
  border: none !important;
}

.k-grid-border-right-width {
  .k-table-row .k-table-td:last-child {
    border-right-width: 1px !important;
  }
}

.k-grid-border-bottom-width {
  .k-table-row:last-child .k-table-td {
    border-bottom-width: 1px !important;
  }
}

kendo-grid.k-grid .k-table-alt-row .k-grid-content-sticky,
kendo-grid.k-grid .k-grid-row-sticky.k-table-alt-row td,
kendo-grid.k-grid .k-grid-row-sticky.k-table-alt-row .k-table-td {
  background-color: white;
}

.k-pager {
  border-top: 1px solid #F2F2F2;
  border-bottom: none;
  border-left: none;
  border-right: none;
  background-color: #FFFFFF !important;

  .k-svg-icon {
    color: #333333 !important;
  }

  kendo-pager-numeric-buttons {
    button {
      color: #4061C7 !important;
      border-radius: 4px;
      cursor: pointer;

      &::before,
      &::after {
        opacity: 0 !important;
      }
    }

    .k-selected {
      background: #E3EEFF !important;
    }
  }

  .k-pager-info,
  label {
    @extend .Body-R;
  }
}

.k-pager:focus,
.k-pager.k-focus {
  box-shadow: none !important;
}

.k-pager-sizes {
  .k-dropdown {
    width: 5.8em !important;
    border-color: #E6E6E6 !important;
  }
}

.custom-kendo-pc-list-grid {
  max-height: calc(100vh - 254px) !important;
}

.custom-kendo-list-grid {
  max-height: calc(100vh - 192px) !important;
}

.custom-kendo-lp-report-grid {
  max-height: calc(100vh - 200px) !important;
}

.custom-kendo-repository-grid {
  max-height: calc(100vh - 394px) !important;
}

.k-grid .k-grid-header .k-table-th {
  position: relative;
  vertical-align: middle !important;
  cursor: default;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.custom-kendo-cab-table-grid {
  max-height: 60vh !important;

  .k-grid-header .k-table-th.k-grid-header-sticky,
  .k-grid-header .k-filter-row .k-grid-header-sticky,
  .k-grid .k-grid-content-sticky,
  .k-grid .k-grid-row-sticky,
  .k-grid .k-grid-footer-sticky {
    position: sticky !important;
  }
}

.k-grid {
  td {
    padding: 12px 16px;
  }
}

.cell-padding {
  padding: 12px 16px !important;
}

#kpi-grid table tbody>tr>td,
.grid-row-no-padding table tbody>tr>td {
  padding: 0px 0px !important;
}

.header-left-padding {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

#kpi-grid.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row):hover,
#esg-kpi-data-grid.esg-f-table .k-table-tbody>.k-table-row:not(.k-detail-row):hover,
.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row).k-hover {
  background-color: white !important;
}

#kpi-grid.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row):hover,
.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row).k-hover {
  background-color: white !important;
}

#kpi-grid.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row):hover td:first-child,
#kpi-grid.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row).k-hover td:first-child,
#esg-kpi-data-grid.esg-f-table .k-table-tbody>.k-table-row:not(.k-detail-row):hover td:first-child,
#esg-kpi-data-grid.esg-f-table .k-table-tbody>.k-table-row:not(.k-detail-row).k-hover td:first-child {
  background-color: white !important;
}

.kendo-grid-kpi-list {
  max-height: calc(100vh - 250px) !important;
}

.kendo-grid-formula-builder {
  min-height: calc(100vh - 355px) !important;
  max-height: calc(100vh - 355px) !important;
}

.custom-kendo-fxrates-table-grid {
  max-height: calc(100vh - 200px) !important;

  .k-grid-header .k-table-th.k-grid-header-sticky,
  .k-grid-header .k-filter-row .k-grid-header-sticky,
  .k-grid .k-grid-content-sticky,
  .k-grid .k-grid-row-sticky,
  .k-grid .k-grid-footer-sticky {
    position: sticky !important;
  }
}

.kendo-work-flow-grid {
  max-height: calc(100vh - 306px) !important;
}

.kendo-internal-report-grid {
  max-height: calc(100vh - 325px) !important;

  .k-grid-header .k-table-th {
    .k-link {
      padding-block: 0rem !important;
      padding-inline: 0rem !important;
    }
  }

  .flex-container-header {
    display: flex;
    justify-content: space-between;
    width: 100% !important;
    align-items: center;
    padding-left: 8px !important;
  }

  .custom-internal-search {
    border-left: 1px solid #DEDFE0 !important;
  }

  .flex-center-vertical {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.k-checkbox:checked,
.k-checkbox.k-checked {
  background-image: url('./../../checkbox.svg') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  border-color: $nep-primary !important;
  background-color: $nep-primary !important;
  padding-left: 2px !important;
}

.k-checkbox {
  padding-left: 2px !important;
}

.k-pl {
  padding-left: 0.5rem !important;
}

.kendo-fund-deal-grid {
  max-height: 200px !important;

  .deal-cell {
    width: calc(100% - 40px);
  }
}

.kendo-fund-tr-grid {
  max-height: 40vh !important;
}

.kendo-deal-tr-grid {
  max-height: 50vh !important;

  td {
    padding: 0 !important;
  }

  .k-cell-padding {
    padding: 12px 16px;
  }
}

table th {
  color: #666666 !important;
  @extend .Body-R;
}

.analytics-grid {
  .k-grid-header {
    padding: 0px !important;
  }
}

.audit-log-grid {
  max-height: calc(100% - 450px);
}

.fixed-calculation-table {
  .k-grid-table {
    tr:last-child {
      position: sticky !important;
      bottom: -3px !important;
      z-index: 999;
    }
  }
}

.highlight-last-row {

  .k-grid-table,
  .k-grid-content {

    tr:last-child,
    tr:nth-last-child(2) {
      td {
        background: #e8eaf7 !important;
      }
    }
  }
}

.top-holding-lastrow {
  .k-grid-table {
    tr:last-child {
      background: #E8EAF6 !important;
      opacity: 1;
      box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
      -webkit-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
      -moz-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;

      .higlighted-cell {
        background: #E8EAF6 !important;
        opacity: 1;
      }

      .k-grid-content-sticky {
        background: #E8EAF6 !important;
        opacity: 1;
        box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
        -webkit-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;
        -moz-box-shadow: 0px -3px 6px #00000014, 0px 3px 6px #00000014;

      }
    }
  }
}

.k-grid-width-60vh {
  max-height: 60vh;
}

.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row):hover,
.theme-light .k-grid .k-table-tbody>.k-table-row:not(.k-detail-row).k-hover {
  background-color: none !important;
}

.custom-holdings-grid-h {
  max-height: calc(100vh - 382px) !important;
}

.status-grid {
  thead {
    display: none;
  }

  .k-virtual-content {
    overflow: auto !important;
  }

  .backgroundStyleOnHover:hover .actions {
    display: block !important;
  }

  .backgroundStyleOnHover:hover {
    background: #EFF0F9 0% 0% no-repeat padding-box !important;
  }
}

.cons-grid-h {
  max-height: calc(100vh - 250px) !important;
}

.valuation-custom-height {
  max-height: calc(100vh - 362px) !important;
}

.workflow-access-height {
  max-height: calc(100vh - 305px) !important;
}


.workflow-border-left {
  border: 1px solid #dee2e6;
}

.k-grid-content {
  .k-table-row {
    &:hover {
      background: #E8EAF6 0% 0% no-repeat padding-box !important;
    }
  }
}

kendo-grid.k-grid .k-grid-content-sticky:hover,
kendo-grid.k-grid .k-grid-content-sticky.k-hover {
  background: #E8EAF6 0% 0% no-repeat padding-box !important;
}

.lp-report-grid {

  .k-grid-content {
    overflow-y: auto;
  }

  .k-table-row:last-child>.k-table-td {
    border-bottom-width: 1px !important;
  }
}

.k-grid-extraction-custom.k-grid-header .k-table-th .k-column-title {
  @extend .Body-M;
  color: $Neutral-Gray-70;

  th:last-child {
    border-right-width: 0 !important;
  }
}

.k-grid-extraction-custom {
  border-radius: 8px;
  overflow: hidden;
}

.custom-ingestion-list-grid {
  height: calc(100vh - 294px) !important;

  .k-table-tbody>.k-table-row:last-child {
    td {
      border-bottom: 1px solid $border-color !important;
      border-bottom-width: 1px !important;
    }
  }

  .k-table-row {

    td:last-child,
    .k-table-td:last-child {
      border-right-width: 1px !important;
      border-right-color: $border-color;
    }
  }
}

.k-grid-header-req {
  .k-column-title {
    &:after {
      padding-left: 2px;
      content: "*";
      color: $Red-80;
    }
  }
}

.custom-ingest-grid tr.k-master-row {
  &:hover {
    cursor: pointer;
  }
}

.custom-ingestion-preview-grid td {
  padding: 6px 16px !important;
}

.custom-ingestion-list-fetchfile-grid {
  height: calc(100vh - 356px) !important;

  .k-table-tbody>.k-table-row:last-child {
    td {
      border-bottom: 1px solid $border-color !important;
      border-bottom-width: 1px !important;
    }
  }

  .k-table-row {

    td:last-child,
    .k-table-td:last-child {
      border-right-width: 1px !important;
      border-right-color: $border-color;
    }
  }
}

.email-noStyleGrid {

  .k-grid th,
  .k-grid-content td,
  .k-grid-header-wrap,
  .k-grid,
  .k-grid tr.k-alt,
  .k-grid-header,
  .k-grid-toolbar {
    border-top: none !important;
    border-left: none !important;
  }

  .k-grid-content {
    .k-table-row {

      &:hover,
      &.k-selected {
        background-color: $light-blue-color !important;
      }

      &.k-selected {

        td,
        .k-table-td {
          background-color: $light-blue-color !important;
        }
      }
    }
  }
}

.custom-kendo-fxrates-table-grid .k-grid-header .k-table-th.k-first {
  border-inline-start-width: 0px !important;
}
.custom-kendo-fxrates-table-grid .k-table-alt-row .k-grid-content-sticky,
.custom-kendo-fxrates-table-grid .k-grid-content-sticky {
  background: $Neutral-Gray-02 !important;

  .content span {
    @extend .Body-R;
    color: $Neutral-Gray-90 !important;
  }
}

// Consolidate repeated styles into variables and mixins for maintainability and clarity

// Define a mixin for column title styles
@mixin kendo-fxrates-column-title($color) {
  color: $color !important;
  font-family: "Helvetica Neue LT W05_55 Roman", sans-serif !important;
  font-weight: 400 !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  letter-spacing: 0 !important;
}

// Sticky header column titles
.custom-kendo-fxrates-table-grid {
  .k-grid-header-sticky {
    .k-column-title {
      @include kendo-fxrates-column-title($Neutral-Gray-60);
    }
  }

  .k-grid-header {
    // Group header cells (not sticky) - first row
    .k-grid-header-table > .k-table-thead > .k-table-row:first-child > .k-table-th:not(.k-grid-header-sticky) {
      background: $Primary-35 !important;
      .k-column-title {
        color: $Neutral-Gray-80 !important;
      }
    }

    // Group header cells (not sticky) - second row
    .k-grid-header-table > .k-table-thead > .k-table-row:nth-child(2) > .k-table-th:not(.k-grid-header-sticky) {
      background: $Neutral-Gray-05 !important;
      .k-column-title {
        @include kendo-fxrates-column-title($Neutral-Gray-90);
      }
    }

    // All first row th (not sticky)
    tr:first-child > th:not([class*="k-grid-header-sticky"]) {
      background: $Primary-35 !important;
      color: $Neutral-Gray-80 !important;
    }
    
    // All second row th (not sticky)
    tr:nth-child(2) > th:not([class*="k-grid-header-sticky"]) {
      background: $Neutral-Gray-05 !important;
      @include kendo-fxrates-column-title($Neutral-Gray-90);
    }
  }
  .k-grid-content {
    .k-table-tbody {
      .k-table-row {
        .k-table-td:not(.k-grid-content-sticky) {
          text-align: right;
          
          .content, span {
            text-align: right;
            justify-content: flex-end;
            display: flex;
          }
        }
      }
    }
  }
}

